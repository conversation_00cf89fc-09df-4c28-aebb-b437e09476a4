.dark-medium-contrast {
  --md-sys-color-primary: rgb(191 224 252);
  --md-sys-color-surface-tint: rgb(170 203 229);
  --md-sys-color-on-primary: rgb(1 41 62);
  --md-sys-color-primary-container: rgb(116 149 174);
  --md-sys-color-on-primary-container: rgb(0 0 0);
  --md-sys-color-secondary: rgb(210 222 234);
  --md-sys-color-on-secondary: rgb(27 39 48);
  --md-sys-color-secondary-container: rgb(134 146 157);
  --md-sys-color-on-secondary-container: rgb(0 0 0);
  --md-sys-color-tertiary: rgb(255 217 154);
  --md-sys-color-on-tertiary: rgb(54 36 0);
  --md-sys-color-tertiary-container: rgb(239 186 87);
  --md-sys-color-on-tertiary-container: rgb(69 47 0);
  --md-sys-color-error: rgb(255 210 204);
  --md-sys-color-on-error: rgb(84 0 3);
  --md-sys-color-error-container: rgb(254 86 74);
  --md-sys-color-on-error-container: rgb(0 0 0);
  --md-sys-color-background: rgb(18 20 21);
  --md-sys-color-on-background: rgb(227 226 228);
  --md-sys-color-surface: rgb(20 19 19);
  --md-sys-color-on-surface: rgb(255 255 255);
  --md-sys-color-surface-variant: rgb(73 71 63);
  --md-sys-color-on-surface-variant: rgb(225 220 210);
  --md-sys-color-outline: rgb(182 177 168);
  --md-sys-color-outline-variant: rgb(148 144 135);
  --md-sys-color-shadow: rgb(0 0 0);
  --md-sys-color-scrim: rgb(0 0 0);
  --md-sys-color-inverse-surface: rgb(229 226 225);
  --md-sys-color-inverse-on-surface: rgb(42 42 41);
  --md-sys-color-inverse-primary: rgb(43 75 98);
  --md-sys-color-primary-fixed: rgb(201 230 255);
  --md-sys-color-on-primary-fixed: rgb(0 19 32);
  --md-sys-color-primary-fixed-dim: rgb(170 203 229);
  --md-sys-color-on-primary-fixed-variant: rgb(23 57 79);
  --md-sys-color-secondary-fixed: rgb(216 228 240);
  --md-sys-color-on-secondary-fixed: rgb(7 18 27);
  --md-sys-color-secondary-fixed-dim: rgb(188 200 212);
  --md-sys-color-on-secondary-fixed-variant: rgb(44 56 65);
  --md-sys-color-tertiary-fixed: rgb(255 222 168);
  --md-sys-color-on-tertiary-fixed: rgb(26 15 0);
  --md-sys-color-tertiary-fixed-dim: rgb(243 190 91);
  --md-sys-color-on-tertiary-fixed-variant: rgb(73 50 0);
  --md-sys-color-surface-dim: rgb(20 19 19);
  --md-sys-color-surface-bright: rgb(69 68 68);
  --md-sys-color-surface-container-lowest: rgb(7 7 7);
  --md-sys-color-surface-container-low: rgb(30 29 29);
  --md-sys-color-surface-container: rgb(40 40 39);
  --md-sys-color-surface-container-high: rgb(51 50 50);
  --md-sys-color-surface-container-highest: rgb(62 61 61);
  --md-extended-color-orange-color: rgb(255 210 199);
  --md-extended-color-orange-on-color: rgb(77 14 0);
  --md-extended-color-orange-color-container: rgb(248 93 48);
  --md-extended-color-orange-on-color-container: rgb(0 0 0);
  --md-extended-color-green-color: rgb(183 230 216);
  --md-extended-color-green-on-color: rgb(0 44 35);
  --md-extended-color-green-color-container: rgb(109 154 141);
  --md-extended-color-green-on-color-container: rgb(0 0 0);
  --md-extended-color-yellow-color: rgb(255 217 160);
  --md-extended-color-yellow-on-color: rgb(55 36 0);
  --md-extended-color-yellow-color-container: rgb(249 183 62);
  --md-extended-color-yellow-on-color-container: rgb(71 47 0);
  --md-extended-color-gold-color: rgb(251 215 160);
  --md-extended-color-gold-on-color: rgb(53 34 0);
  --md-extended-color-gold-color-container: rgb(187 155 105);
  --md-extended-color-gold-on-color-container: rgb(29 17 0);
  --md-extended-color-beige-color: rgb(245 237 216);
  --md-extended-color-beige-on-color: rgb(52 48 35);
  --md-extended-color-beige-color-container: rgb(216 209 189);
  --md-extended-color-beige-on-color-container: rgb(65 61 47);
  --md-extended-color-creme-color: rgb(255 255 255);
  --md-extended-color-creme-on-color: rgb(47 49 41);
  --md-extended-color-creme-color-container: rgb(227 227 216);
  --md-extended-color-creme-on-color-container: rgb(71 73 64);
}
