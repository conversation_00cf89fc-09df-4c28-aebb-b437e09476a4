.light-high-contrast {
  --md-sys-color-primary: rgb(0 18 30);
  --md-sys-color-surface-tint: rgb(66 98 121);
  --md-sys-color-on-primary: rgb(255 255 255);
  --md-sys-color-primary-container: rgb(0 40 61);
  --md-sys-color-on-primary-container: rgb(191 224 251);
  --md-sys-color-secondary: rgb(34 45 55);
  --md-sys-color-on-secondary: rgb(255 255 255);
  --md-sys-color-secondary-container: rgb(63 75 84);
  --md-sys-color-on-secondary-container: rgb(255 255 255);
  --md-sys-color-tertiary: rgb(60 41 0);
  --md-sys-color-on-tertiary: rgb(255 255 255);
  --md-sys-color-tertiary-container: rgb(97 68 0);
  --md-sys-color-on-tertiary-container: rgb(255 255 255);
  --md-sys-color-error: rgb(96 0 4);
  --md-sys-color-on-error: rgb(255 255 255);
  --md-sys-color-error-container: rgb(151 2 12);
  --md-sys-color-on-error-container: rgb(255 255 255);
  --md-sys-color-background: rgb(250 249 251);
  --md-sys-color-on-background: rgb(26 28 29);
  --md-sys-color-surface: rgb(252 248 247);
  --md-sys-color-on-surface: rgb(0 0 0);
  --md-sys-color-surface-variant: rgb(231 226 216);
  --md-sys-color-on-surface-variant: rgb(0 0 0);
  --md-sys-color-outline: rgb(46 44 37);
  --md-sys-color-outline-variant: rgb(75 73 65);
  --md-sys-color-shadow: rgb(0 0 0);
  --md-sys-color-scrim: rgb(0 0 0);
  --md-sys-color-inverse-surface: rgb(49 48 48);
  --md-sys-color-inverse-on-surface: rgb(255 255 255);
  --md-sys-color-inverse-primary: rgb(170 203 229);
  --md-sys-color-primary-fixed: rgb(44 76 99);
  --md-sys-color-on-primary-fixed: rgb(255 255 255);
  --md-sys-color-primary-fixed-dim: rgb(18 54 75);
  --md-sys-color-on-primary-fixed-variant: rgb(255 255 255);
  --md-sys-color-secondary-fixed: rgb(63 75 84);
  --md-sys-color-on-secondary-fixed: rgb(255 255 255);
  --md-sys-color-secondary-fixed-dim: rgb(40 52 61);
  --md-sys-color-on-secondary-fixed-variant: rgb(255 255 255);
  --md-sys-color-tertiary-fixed: rgb(97 68 0);
  --md-sys-color-on-tertiary-fixed: rgb(255 255 255);
  --md-sys-color-tertiary-fixed-dim: rgb(69 47 0);
  --md-sys-color-on-tertiary-fixed-variant: rgb(255 255 255);
  --md-sys-color-surface-dim: rgb(187 184 183);
  --md-sys-color-surface-bright: rgb(252 248 247);
  --md-sys-color-surface-container-lowest: rgb(255 255 255);
  --md-sys-color-surface-container-low: rgb(244 240 239);
  --md-sys-color-surface-container: rgb(229 226 225);
  --md-sys-color-surface-container-high: rgb(215 212 211);
  --md-sys-color-surface-container-highest: rgb(201 198 197);
  --md-extended-color-orange-color: rgb(88 18 0);
  --md-extended-color-orange-on-color: rgb(255 255 255);
  --md-extended-color-orange-color-container: rgb(140 34 0);
  --md-extended-color-orange-on-color-container: rgb(255 255 255);
  --md-extended-color-green-color: rgb(0 42 34);
  --md-extended-color-green-on-color: rgb(255 255 255);
  --md-extended-color-green-color-container: rgb(18 65 55);
  --md-extended-color-green-on-color-container: rgb(241 255 249);
  --md-extended-color-yellow-color: rgb(61 40 0);
  --md-extended-color-yellow-on-color: rgb(255 255 255);
  --md-extended-color-yellow-color-container: rgb(99 67 0);
  --md-extended-color-yellow-on-color-container: rgb(255 255 255);
  --md-extended-color-gold-color: rgb(61 40 2);
  --md-extended-color-gold-on-color: rgb(255 255 255);
  --md-extended-color-gold-color-container: rgb(93 69 27);
  --md-extended-color-gold-on-color-container: rgb(255 255 255);
  --md-extended-color-beige-color: rgb(47 44 31);
  --md-extended-color-beige-on-color: rgb(255 255 255);
  --md-extended-color-beige-color-container: rgb(77 73 58);
  --md-extended-color-beige-on-color-container: rgb(255 255 255);
  --md-extended-color-creme-color: rgb(43 45 37);
  --md-extended-color-creme-on-color: rgb(255 255 255);
  --md-extended-color-creme-color-container: rgb(72 74 65);
  --md-extended-color-creme-on-color-container: rgb(255 255 255);
}
