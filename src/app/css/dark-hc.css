.dark-high-contrast {
  --md-sys-color-primary: rgb(228 242 255);
  --md-sys-color-surface-tint: rgb(170 203 229);
  --md-sys-color-on-primary: rgb(0 0 0);
  --md-sys-color-primary-container: rgb(166 199 225);
  --md-sys-color-on-primary-container: rgb(0 13 23);
  --md-sys-color-secondary: rgb(229 241 254);
  --md-sys-color-on-secondary: rgb(0 0 0);
  --md-sys-color-secondary-container: rgb(184 196 208);
  --md-sys-color-on-secondary-container: rgb(2 12 21);
  --md-sys-color-tertiary: rgb(255 238 214);
  --md-sys-color-on-tertiary: rgb(0 0 0);
  --md-sys-color-tertiary-container: rgb(239 186 87);
  --md-sys-color-on-tertiary-container: rgb(18 9 0);
  --md-sys-color-error: rgb(255 236 233);
  --md-sys-color-on-error: rgb(0 0 0);
  --md-sys-color-error-container: rgb(255 174 164);
  --md-sys-color-on-error-container: rgb(20 0 0);
  --md-sys-color-background: rgb(18 20 21);
  --md-sys-color-on-background: rgb(227 226 228);
  --md-sys-color-surface: rgb(20 19 19);
  --md-sys-color-on-surface: rgb(255 255 255);
  --md-sys-color-surface-variant: rgb(73 71 63);
  --md-sys-color-on-surface-variant: rgb(255 255 255);
  --md-sys-color-outline: rgb(245 239 229);
  --md-sys-color-outline-variant: rgb(199 194 184);
  --md-sys-color-shadow: rgb(0 0 0);
  --md-sys-color-scrim: rgb(0 0 0);
  --md-sys-color-inverse-surface: rgb(229 226 225);
  --md-sys-color-inverse-on-surface: rgb(0 0 0);
  --md-sys-color-inverse-primary: rgb(43 75 98);
  --md-sys-color-primary-fixed: rgb(201 230 255);
  --md-sys-color-on-primary-fixed: rgb(0 0 0);
  --md-sys-color-primary-fixed-dim: rgb(170 203 229);
  --md-sys-color-on-primary-fixed-variant: rgb(0 19 32);
  --md-sys-color-secondary-fixed: rgb(216 228 240);
  --md-sys-color-on-secondary-fixed: rgb(0 0 0);
  --md-sys-color-secondary-fixed-dim: rgb(188 200 212);
  --md-sys-color-on-secondary-fixed-variant: rgb(7 18 27);
  --md-sys-color-tertiary-fixed: rgb(255 222 168);
  --md-sys-color-on-tertiary-fixed: rgb(0 0 0);
  --md-sys-color-tertiary-fixed-dim: rgb(243 190 91);
  --md-sys-color-on-tertiary-fixed-variant: rgb(26 15 0);
  --md-sys-color-surface-dim: rgb(20 19 19);
  --md-sys-color-surface-bright: rgb(81 80 79);
  --md-sys-color-surface-container-lowest: rgb(0 0 0);
  --md-sys-color-surface-container-low: rgb(32 31 31);
  --md-sys-color-surface-container: rgb(49 48 48);
  --md-sys-color-surface-container-high: rgb(60 59 59);
  --md-sys-color-surface-container-highest: rgb(72 70 70);
  --md-extended-color-orange-color: rgb(255 236 231);
  --md-extended-color-orange-on-color: rgb(0 0 0);
  --md-extended-color-orange-color-container: rgb(255 175 153);
  --md-extended-color-orange-on-color-container: rgb(30 3 0);
  --md-extended-color-green-color: rgb(202 250 235);
  --md-extended-color-green-on-color: rgb(0 0 0);
  --md-extended-color-green-color-container: rgb(158 204 190);
  --md-extended-color-green-on-color-container: rgb(0 14 10);
  --md-extended-color-yellow-color: rgb(255 237 215);
  --md-extended-color-yellow-on-color: rgb(0 0 0);
  --md-extended-color-yellow-color-container: rgb(249 183 62);
  --md-extended-color-yellow-on-color-container: rgb(19 10 0);
  --md-extended-color-gold-color: rgb(255 237 215);
  --md-extended-color-gold-on-color: rgb(0 0 0);
  --md-extended-color-gold-color-container: rgb(224 189 137);
  --md-extended-color-gold-on-color-container: rgb(19 9 0);
  --md-extended-color-beige-color: rgb(247 240 219);
  --md-extended-color-beige-on-color: rgb(0 0 0);
  --md-extended-color-beige-color-container: rgb(216 209 189);
  --md-extended-color-beige-on-color-container: rgb(32 29 16);
  --md-extended-color-creme-color: rgb(255 255 255);
  --md-extended-color-creme-on-color: rgb(0 0 0);
  --md-extended-color-creme-color-container: rgb(227 227 216);
  --md-extended-color-creme-on-color-container: rgb(41 43 35);
}
