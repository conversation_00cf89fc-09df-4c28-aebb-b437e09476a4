.dark {
  --md-sys-color-primary: rgb(170 203 229);
  --md-sys-color-surface-tint: rgb(170 203 229);
  --md-sys-color-on-primary: rgb(15 51 73);
  --md-sys-color-primary-container: rgb(0 40 61);
  --md-sys-color-on-primary-container: rgb(112 144 169);
  --md-sys-color-secondary: rgb(188 200 212);
  --md-sys-color-on-secondary: rgb(38 50 59);
  --md-sys-color-secondary-container: rgb(60 72 82);
  --md-sys-color-on-secondary-container: rgb(170 183 194);
  --md-sys-color-tertiary: rgb(255 217 154);
  --md-sys-color-on-tertiary: rgb(66 45 0);
  --md-sys-color-tertiary-container: rgb(239 186 87);
  --md-sys-color-on-tertiary-container: rgb(106 74 0);
  --md-sys-color-error: rgb(255 180 171);
  --md-sys-color-on-error: rgb(105 0 5);
  --md-sys-color-error-container: rgb(218 60 52);
  --md-sys-color-on-error-container: rgb(20 0 0);
  --md-sys-color-background: rgb(18 20 21);
  --md-sys-color-on-background: rgb(227 226 228);
  --md-sys-color-surface: rgb(20 19 19);
  --md-sys-color-on-surface: rgb(229 226 225);
  --md-sys-color-surface-variant: rgb(73 71 63);
  --md-sys-color-on-surface-variant: rgb(203 198 188);
  --md-sys-color-outline: rgb(148 144 135);
  --md-sys-color-outline-variant: rgb(73 71 63);
  --md-sys-color-shadow: rgb(0 0 0);
  --md-sys-color-scrim: rgb(0 0 0);
  --md-sys-color-inverse-surface: rgb(229 226 225);
  --md-sys-color-inverse-on-surface: rgb(49 48 48);
  --md-sys-color-inverse-primary: rgb(66 98 121);
  --md-sys-color-primary-fixed: rgb(201 230 255);
  --md-sys-color-on-primary-fixed: rgb(0 30 47);
  --md-sys-color-primary-fixed-dim: rgb(170 203 229);
  --md-sys-color-on-primary-fixed-variant: rgb(41 74 96);
  --md-sys-color-secondary-fixed: rgb(216 228 240);
  --md-sys-color-on-secondary-fixed: rgb(17 29 37);
  --md-sys-color-secondary-fixed-dim: rgb(188 200 212);
  --md-sys-color-on-secondary-fixed-variant: rgb(60 72 82);
  --md-sys-color-tertiary-fixed: rgb(255 222 168);
  --md-sys-color-on-tertiary-fixed: rgb(39 25 0);
  --md-sys-color-tertiary-fixed-dim: rgb(243 190 91);
  --md-sys-color-on-tertiary-fixed-variant: rgb(94 66 0);
  --md-sys-color-surface-dim: rgb(20 19 19);
  --md-sys-color-surface-bright: rgb(58 57 56);
  --md-sys-color-surface-container-lowest: rgb(14 14 14);
  --md-sys-color-surface-container-low: rgb(28 27 27);
  --md-sys-color-surface-container: rgb(32 31 31);
  --md-sys-color-surface-container-high: rgb(42 42 41);
  --md-sys-color-surface-container-highest: rgb(53 52 52);
  --md-extended-color-orange-color: rgb(255 181 160);
  --md-extended-color-orange-on-color: rgb(96 20 0);
  --md-extended-color-orange-color-container: rgb(248 93 48);
  --md-extended-color-orange-on-color-container: rgb(60 9 0);
  --md-extended-color-green-color: rgb(161 208 194);
  --md-extended-color-green-on-color: rgb(4 55 46);
  --md-extended-color-green-color-container: rgb(18 65 55);
  --md-extended-color-green-on-color-container: rgb(127 173 160);
  --md-extended-color-yellow-color: rgb(255 217 160);
  --md-extended-color-yellow-on-color: rgb(67 44 0);
  --md-extended-color-yellow-color-container: rgb(249 183 62);
  --md-extended-color-yellow-on-color-container: rgb(108 73 0);
  --md-extended-color-gold-color: rgb(228 193 140);
  --md-extended-color-gold-on-color: rgb(65 45 4);
  --md-extended-color-gold-color-container: rgb(187 155 105);
  --md-extended-color-gold-on-color-container: rgb(73 51 10);
  --md-extended-color-beige-color: rgb(245 237 216);
  --md-extended-color-beige-on-color: rgb(52 48 35);
  --md-extended-color-beige-color-container: rgb(216 209 189);
  --md-extended-color-beige-on-color-container: rgb(94 89 74);
  --md-extended-color-creme-color: rgb(255 255 255);
  --md-extended-color-creme-on-color: rgb(47 49 41);
  --md-extended-color-creme-color-container: rgb(227 227 216);
  --md-extended-color-creme-on-color-container: rgb(100 101 92);
}
