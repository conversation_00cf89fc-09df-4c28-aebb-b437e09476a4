:root {
  --md-sys-color-primary: rgb(0 18 30);
  --md-sys-color-surface-tint: rgb(66 98 121);
  --md-sys-color-on-primary: rgb(255 255 255);
  --md-sys-color-primary-container: rgb(0 40 61);
  --md-sys-color-on-primary-container: rgb(112 144 169);
  --md-sys-color-secondary: rgb(84 96 106);
  --md-sys-color-on-secondary: rgb(255 255 255);
  --md-sys-color-secondary-container: rgb(213 225 237);
  --md-sys-color-on-secondary-container: rgb(88 100 110);
  --md-sys-color-tertiary: rgb(124 88 0);
  --md-sys-color-on-tertiary: rgb(255 255 255);
  --md-sys-color-tertiary-container: rgb(239 186 87);
  --md-sys-color-on-tertiary-container: rgb(106 74 0);
  --md-sys-color-error: rgb(183 33 31);
  --md-sys-color-on-error: rgb(255 255 255);
  --md-sys-color-error-container: rgb(218 60 52);
  --md-sys-color-on-error-container: rgb(20 0 0);
  --md-sys-color-background: rgb(250 249 251);
  --md-sys-color-on-background: rgb(26 28 29);
  --md-sys-color-surface: rgb(252 248 247);
  --md-sys-color-on-surface: rgb(28 27 27);
  --md-sys-color-surface-variant: rgb(231 226 216);
  --md-sys-color-on-surface-variant: rgb(73 71 63);
  --md-sys-color-outline: rgb(122 119 110);
  --md-sys-color-outline-variant: rgb(203 198 188);
  --md-sys-color-shadow: rgb(0 0 0);
  --md-sys-color-scrim: rgb(0 0 0);
  --md-sys-color-inverse-surface: rgb(49 48 48);
  --md-sys-color-inverse-on-surface: rgb(244 240 239);
  --md-sys-color-inverse-primary: rgb(170 203 229);
  --md-sys-color-primary-fixed: rgb(201 230 255);
  --md-sys-color-on-primary-fixed: rgb(0 30 47);
  --md-sys-color-primary-fixed-dim: rgb(170 203 229);
  --md-sys-color-on-primary-fixed-variant: rgb(41 74 96);
  --md-sys-color-secondary-fixed: rgb(216 228 240);
  --md-sys-color-on-secondary-fixed: rgb(17 29 37);
  --md-sys-color-secondary-fixed-dim: rgb(188 200 212);
  --md-sys-color-on-secondary-fixed-variant: rgb(60 72 82);
  --md-sys-color-tertiary-fixed: rgb(255 222 168);
  --md-sys-color-on-tertiary-fixed: rgb(39 25 0);
  --md-sys-color-tertiary-fixed-dim: rgb(243 190 91);
  --md-sys-color-on-tertiary-fixed-variant: rgb(94 66 0);
  --md-sys-color-surface-dim: rgb(221 217 216);
  --md-sys-color-surface-bright: rgb(252 248 247);
  --md-sys-color-surface-container-lowest: rgb(255 255 255);
  --md-sys-color-surface-container-low: rgb(247 243 242);
  --md-sys-color-surface-container: rgb(241 237 236);
  --md-sys-color-surface-container-high: rgb(235 231 230);
  --md-sys-color-surface-container-highest: rgb(229 226 225);
  --md-extended-color-orange-color: rgb(173 44 0);
  --md-extended-color-orange-on-color: rgb(255 255 255);
  --md-extended-color-orange-color-container: rgb(210 66 22);
  --md-extended-color-orange-on-color-container: rgb(255 251 255);
  --md-extended-color-green-color: rgb(0 42 34);
  --md-extended-color-green-on-color: rgb(255 255 255);
  --md-extended-color-green-color-container: rgb(18 65 55);
  --md-extended-color-green-on-color-container: rgb(127 173 160);
  --md-extended-color-yellow-color: rgb(126 87 0);
  --md-extended-color-yellow-on-color: rgb(255 255 255);
  --md-extended-color-yellow-color-container: rgb(249 183 62);
  --md-extended-color-yellow-on-color-container: rgb(108 73 0);
  --md-extended-color-gold-color: rgb(116 90 46);
  --md-extended-color-gold-on-color: rgb(255 255 255);
  --md-extended-color-gold-color-container: rgb(187 155 105);
  --md-extended-color-gold-on-color-container: rgb(73 51 10);
  --md-extended-color-beige-color: rgb(99 94 78);
  --md-extended-color-beige-on-color: rgb(255 255 255);
  --md-extended-color-beige-color-container: rgb(216 209 189);
  --md-extended-color-beige-on-color-container: rgb(94 89 74);
  --md-extended-color-creme-color: rgb(94 95 86);
  --md-extended-color-creme-on-color: rgb(255 255 255);
  --md-extended-color-creme-color-container: rgb(249 249 237);
  --md-extended-color-creme-on-color-container: rgb(113 115 105);
}
