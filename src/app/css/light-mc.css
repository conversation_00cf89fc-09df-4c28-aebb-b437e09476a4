.light-medium-contrast {
  --md-sys-color-primary: rgb(0 18 30);
  --md-sys-color-surface-tint: rgb(66 98 121);
  --md-sys-color-on-primary: rgb(255 255 255);
  --md-sys-color-primary-container: rgb(0 40 61);
  --md-sys-color-on-primary-container: rgb(148 180 207);
  --md-sys-color-secondary: rgb(44 56 65);
  --md-sys-color-on-secondary: rgb(255 255 255);
  --md-sys-color-secondary-container: rgb(99 110 121);
  --md-sys-color-on-secondary-container: rgb(255 255 255);
  --md-sys-color-tertiary: rgb(73 50 0);
  --md-sys-color-on-tertiary: rgb(255 255 255);
  --md-sys-color-tertiary-container: rgb(143 101 0);
  --md-sys-color-on-tertiary-container: rgb(255 255 255);
  --md-sys-color-error: rgb(116 0 6);
  --md-sys-color-on-error: rgb(255 255 255);
  --md-sys-color-error-container: rgb(204 50 43);
  --md-sys-color-on-error-container: rgb(255 255 255);
  --md-sys-color-background: rgb(250 249 251);
  --md-sys-color-on-background: rgb(26 28 29);
  --md-sys-color-surface: rgb(252 248 247);
  --md-sys-color-on-surface: rgb(17 17 17);
  --md-sys-color-surface-variant: rgb(231 226 216);
  --md-sys-color-on-surface-variant: rgb(56 54 47);
  --md-sys-color-outline: rgb(85 82 75);
  --md-sys-color-outline-variant: rgb(112 109 100);
  --md-sys-color-shadow: rgb(0 0 0);
  --md-sys-color-scrim: rgb(0 0 0);
  --md-sys-color-inverse-surface: rgb(49 48 48);
  --md-sys-color-inverse-on-surface: rgb(244 240 239);
  --md-sys-color-inverse-primary: rgb(170 203 229);
  --md-sys-color-primary-fixed: rgb(81 113 137);
  --md-sys-color-on-primary-fixed: rgb(255 255 255);
  --md-sys-color-primary-fixed-dim: rgb(56 88 111);
  --md-sys-color-on-primary-fixed-variant: rgb(255 255 255);
  --md-sys-color-secondary-fixed: rgb(99 110 121);
  --md-sys-color-on-secondary-fixed: rgb(255 255 255);
  --md-sys-color-secondary-fixed-dim: rgb(74 86 96);
  --md-sys-color-on-secondary-fixed-variant: rgb(255 255 255);
  --md-sys-color-tertiary-fixed: rgb(143 101 0);
  --md-sys-color-on-tertiary-fixed: rgb(255 255 255);
  --md-sys-color-tertiary-fixed-dim: rgb(112 79 0);
  --md-sys-color-on-tertiary-fixed-variant: rgb(255 255 255);
  --md-sys-color-surface-dim: rgb(201 198 197);
  --md-sys-color-surface-bright: rgb(252 248 247);
  --md-sys-color-surface-container-lowest: rgb(255 255 255);
  --md-sys-color-surface-container-low: rgb(247 243 242);
  --md-sys-color-surface-container: rgb(235 231 230);
  --md-sys-color-surface-container-high: rgb(224 220 219);
  --md-sys-color-surface-container-highest: rgb(212 209 208);
  --md-extended-color-orange-color: rgb(106 23 0);
  --md-extended-color-orange-on-color: rgb(255 255 255);
  --md-extended-color-orange-color-container: rgb(199 59 14);
  --md-extended-color-orange-on-color-container: rgb(255 255 255);
  --md-extended-color-green-color: rgb(0 42 34);
  --md-extended-color-green-on-color: rgb(255 255 255);
  --md-extended-color-green-color-container: rgb(18 65 55);
  --md-extended-color-green-on-color-container: rgb(166 213 199);
  --md-extended-color-yellow-color: rgb(74 49 0);
  --md-extended-color-yellow-on-color: rgb(255 255 255);
  --md-extended-color-yellow-color-container: rgb(145 100 0);
  --md-extended-color-yellow-on-color-container: rgb(255 255 255);
  --md-extended-color-gold-color: rgb(72 50 9);
  --md-extended-color-gold-on-color: rgb(255 255 255);
  --md-extended-color-gold-color-container: rgb(132 105 59);
  --md-extended-color-gold-on-color-container: rgb(255 255 255);
  --md-extended-color-beige-color: rgb(58 54 40);
  --md-extended-color-beige-on-color: rgb(255 255 255);
  --md-extended-color-beige-color-container: rgb(114 109 92);
  --md-extended-color-beige-on-color-container: rgb(255 255 255);
  --md-extended-color-creme-color: rgb(53 55 47);
  --md-extended-color-creme-on-color: rgb(255 255 255);
  --md-extended-color-creme-color-container: rgb(108 110 100);
  --md-extended-color-creme-on-color-container: rgb(255 255 255);
}
