import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import {provideNativeDateAdapter} from '@angular/material/core';
import { MatTimepickerModule } from '@angular/material/timepicker';
import {FormControl, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {Observable} from "rxjs";
import {map, startWith} from "rxjs/operators";
import {AsyncPipe} from '@angular/common';
import {MatAutocompleteModule} from '@angular/material/autocomplete';
import {MatButtonModule} from '@angular/material/button';
import {MatDividerModule} from '@angular/material/divider';

// export interface State {
//   flag: string;
//   name: string;
//   population: string;
// }

@Component({
  selector: 'app-contact-form',
  imports: [MatIconModule, 
            RouterLink, 
            MatFormFieldModule, 
            MatInputModule, 
            MatDatepickerModule, 
            MatTimepickerModule, 
            FormsModule, 
            ReactiveFormsModule, 
            AsyncPipe, 
            MatAutocompleteModule, 
            MatButtonModule,
            MatDividerModule],
  providers: [provideNativeDateAdapter()],
  templateUrl: './contact-form.html',
  styleUrl: './contact-form.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContactForm implements OnInit {
  constructor() {
    const iconRegistry = inject(MatIconRegistry);
    const sanitizer = inject(DomSanitizer);

    iconRegistry.addSvgIcon('arrow_back', sanitizer.bypassSecurityTrustResourceUrl('../../assets/icons/arrow_back.svg'));
  }
  myControl = new FormControl('');
  options: string[] = ['One', 'Two', 'Three'];
  options2: string[] = ['One', 'Two', 'Three'];
  filteredOptions: Observable<string[]> | undefined;
  filteredOptions2: Observable<string[]> | undefined;

  ngOnInit() {
    this.filteredOptions = this.myControl.valueChanges.pipe(
      startWith(''),
      map(value => this._filter(value || '')),
    );
  }

  private _filter(value: string): string[] {
    const filterValue = value.toLowerCase();

    return this.options.filter(option => option.toLowerCase().includes(filterValue));
  }

}
