<div class="surface">
    <span class="detail-header">
        <mat-icon class="header-icon" svgIcon="arrow_back" aria-hidden="false" aria-label="Back Icon"
            style="cursor: pointer;" routerLink="/offender-detail"></mat-icon>
        <span class="title_large">Verify Information</span>
    </span>
    <div class="body_large">Review the information for this supervision contact entry.</div>
    <form class="example-form">
        <mat-form-field class="contact_form_input_field">
            <mat-label>Choose a date</mat-label>
            <input matInput [matDatepicker]="picker">
            <mat-hint style="font-size: 10px; line-height: 0px; margin-top: 0px;">MM/DD/YYYY</mat-hint>
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>

        <mat-form-field class="contact_form_input_field">
            <mat-label>Pick a time</mat-label>
            <input matInput [matTimepicker]="timePicker">
            <mat-timepicker-toggle matIconSuffix [for]="timePicker" />
            <mat-timepicker #timePicker />
        </mat-form-field>

        <mat-form-field class="contact_form_input_field">
            <mat-label>
                <!-- <mat-icon class="input-field-icon" svgIcon="search" aria-hidden="false" aria-label="Search Icon"></mat-icon> -->
                Primary Interviewer</mat-label>
            <input type="text" placeholder="Start typing a name" aria-label="Primary Interviewer" matInput
                [formControl]="myControl" [matAutocomplete]="auto">
            <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete">
                @for (option of filteredOptions | async; track option) {
                <mat-option [value]="option">{{option}}</mat-option>
                }
            </mat-autocomplete>
            <mat-icon matPrefix>search</mat-icon>
        </mat-form-field>
        <mat-form-field class="contact_form_input_field">
            <mat-label>
                <!-- <mat-icon class="input-field-icon" svgIcon="search" aria-hidden="false" aria-label="Search Icon"></mat-icon> -->
                Secondary Interviewer</mat-label>
            <input type="text" placeholder="Start typing a name" aria-label="Secondary Interviewer" matInput
                [formControl]="myControl" [matAutocomplete]="auto">
            <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete">
                @for (option of filteredOptions2 | async; track option) {
                <mat-option [value]="option">{{option}}</mat-option>
                }
            </mat-autocomplete>
            <mat-icon matPrefix>search</mat-icon>
        </mat-form-field>
        <mat-form-field class="contact_form_input_field">
            <mat-label>Contact Type</mat-label>
            <select matNativeControl required>
                <option value="Field_Visit">Field Visit</option>
                <option value="saab">Saab</option>
                <option value="mercedes">Mercedes</option>
                <option value="audi">Audi</option>
            </select>
        </mat-form-field>
        <mat-form-field class="contact_form_input_field">
            <mat-label>Location</mat-label>
            <select matNativeControl required>
                <option value="Primary_Residence">Primary Residence</option>
                <option value="saab">Saab</option>
                <option value="mercedes">Mercedes</option>
                <option value="audi">Audi</option>
            </select>
        </mat-form-field>
    </form>
    <div>
        <mat-divider style="color: lightgray;"></mat-divider>
        <section>
            <div class="button-row">
                <button matButton="outlined">Cancel</button>
                <button style="background-color: #030303; color: white;" matButton="filled" routerLink="/commentary-form">Next</button>
            </div>
        </section>
    </div>
</div>