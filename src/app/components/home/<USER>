import {Component, inject, OnInit} from '@angular/core';
import {MatToolbarModule} from '@angular/material/toolbar';
import {MatIconModule} from '@angular/material/icon';
import {MatDividerModule} from '@angular/material/divider';
import {MatIconRegistry} from '@angular/material/icon';
import {DomSanitizer} from '@angular/platform-browser';
import { OtherOffendersList } from "../other-offenders-list/other-offenders-list";
import { MyCaseload } from "../my-caseload/my-caseload";
import { Agent } from "../../model/agent";


@Component({
  selector: 'app-home',
  templateUrl: './home.html',
  styleUrl: './home.scss',
  imports: [MatToolbarModule, MatIconModule, MatDividerModule, OtherOffendersList, MyCaseload]
})
export class Home implements OnInit {
  agent: Agent = {
    agentId: 'jshardlow',
    firstName: '<PERSON>',
    lastName: 'Shardlow',
    image: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    supervisorId: 'kchunt',
    myCaseload: [] = [
    {ofndrNum: 1, firstName: 'Yogi', lastName: 'Bear', image: '', address: '123 Main St.', city: 'Salt Lake City', state: 'UT', zip: '', phone: '', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 2, firstName: 'Papa', lastName: 'Smurf', image: '', address: '234 Main St.', city: 'Salt Lake City', state: 'UT', zip: '84101', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 3, firstName: 'Sneezy', lastName: 'Dwarf', image: '', address: '345 Center St.', city: 'Salt Lake City', state: 'UT', zip: '84101', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 4, firstName: 'Sleepy', lastName: 'Dwarf', image: '', address: '1234 West Temple', city: 'South Salt Lake City', state: 'UT', zip: '84115', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 5, firstName: 'Smurfette', lastName: 'Smurf', image: '', address: '9212 700 E.', city: 'Magna', state: 'UT', zip: '84111', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 6, firstName: 'Dopey', lastName: 'Dwarf', image: '', address: '67 American Ave.', city: 'Murray', state: 'UT', zip: '84123', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 7, firstName: 'Grumpy', lastName: 'Dwarf', image: '', address: '12324 Minuteman Dr.', city: 'Draper', state: 'UT', zip: '84044', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 8, firstName: 'Bashful', lastName: 'Dwarf', image: '', address: '8523 Redwood Rd.', city: 'South Jordan', state: 'UT', zip: '84088', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 9, firstName: 'Happy', lastName: 'Dwarf', image: '', address: '735 500W', city: 'Moab', state: 'UT', zip: '84452', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 10, firstName: 'Snow', lastName: 'White', image: '', address: '345 State St.', city: 'Logan', state: 'UT', zip: '84567', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []}
  ],
    otherOffenders: []= [
    {ofndrNum: 1, firstName: 'Yogi', lastName: 'Bear', image: '', address: '123 Main St.', city: 'Salt Lake City', state: 'UT', zip: '', phone: '', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 2, firstName: 'Papa', lastName: 'Smurf', image: '', address: '234 Main St.', city: 'Salt Lake City', state: 'UT', zip: '84101', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 3, firstName: 'Sneezy', lastName: 'Dwarf', image: '', address: '345 Center St.', city: 'Salt Lake City', state: 'UT', zip: '84101', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 4, firstName: 'Sleepy', lastName: 'Dwarf', image: '', address: '1234 West Temple', city: 'South Salt Lake City', state: 'UT', zip: '84115', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 5, firstName: 'Smurfette', lastName: 'Smurf', image: '', address: '9212 700 E.', city: 'Magna', state: 'UT', zip: '84111', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 6, firstName: 'Dopey', lastName: 'Dwarf', image: '', address: '67 American Ave.', city: 'Murray', state: 'UT', zip: '84123', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 7, firstName: 'Grumpy', lastName: 'Dwarf', image: '', address: '12324 Minuteman Dr.', city: 'Draper', state: 'UT', zip: '84044', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 8, firstName: 'Bashful', lastName: 'Dwarf', image: '', address: '8523 Redwood Rd.', city: 'South Jordan', state: 'UT', zip: '84088', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 9, firstName: 'Happy', lastName: 'Dwarf', image: '', address: '735 500W', city: 'Moab', state: 'UT', zip: '84452', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []},
    {ofndrNum: 10, firstName: 'Snow', lastName: 'White', image: '', address: '345 State St.', city: 'Logan', state: 'UT', zip: '84567', phone: '************', lastSuccessfulContactDate: new Date(), contactArray: []}
  ]
  };
  agentInitials: string = ''

  constructor() {
    const iconRegistry = inject(MatIconRegistry);
    const sanitizer = inject(DomSanitizer);

    iconRegistry.addSvgIcon('bell', sanitizer.bypassSecurityTrustResourceUrl('../../assets/icons/bell.svg'));
    iconRegistry.addSvgIcon('search', sanitizer.bypassSecurityTrustResourceUrl('../../assets/icons/search.svg'));
  }
  ngOnInit(): void {
    this.agentInitials = this.agent.firstName.substring(0, 1) + this.agent.lastName.substring(0, 1);
  }
}
