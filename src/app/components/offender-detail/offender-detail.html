<div class="surface">
    <span class="detail-header">
        <mat-icon class="header-icon" svgIcon="arrow_back" aria-hidden="false" aria-label="Back Icon"
            style="cursor: pointer;" routerLink="/"></mat-icon>
        <span class="title_large">Offender Panel</span>
    </span>
    <div>
        <div class="detail-card">
            <div style="display: flex;">
                <div style="flex: 1;">
                    <img>
                </div>
                <div style="flex: 2;">
                    <div class="label_medium">215915</div>
                    <div class="title_xlarge">Bear, Yogi</div>
                    <div class="label_medium">Last Successful Contact: 04/12/2025</div>
                </div>
            </div>
            <br />
            <button class="add-contact-button" routerLink="/contact-form" style="cursor: pointer;" matRipple
                [matRippleCentered]="centered" [matRippleDisabled]="disabled" [matRippleUnbounded]="unbounded"
                [matRippleRadius]="radius" [matRippleColor]="color"><mat-icon class="state-icon" svgIcon="add-white"
                    aria-hidden="false" aria-label="Notifications Icon"></mat-icon>Supervision Contact Entry</button>
        </div>
        <br />
        <div class="detail-card">
            <span class="label_large_prominent">Contact Information</span>
            <div style="display: flex; margin-top: 15px;">
                <div style="flex: 0;">
                    <mat-icon class="header-icon" svgIcon="phone" aria-hidden="false" aria-label="Back Icon"
                        style="cursor: pointer;"></mat-icon>
                </div>
                <div style="flex: 1;">
                    <div class="label_medium">Primary Phone</div>
                    <div class="body_small">************</div>
                </div>
            </div>
            <div style="display: flex; margin-top: 15px">
                <div style="flex: 0;">
                    <mat-icon class="header-icon" svgIcon="location_on" aria-hidden="false" aria-label="Back Icon"
                        style="cursor: pointer;"></mat-icon>
                </div>

                <div style="flex: 1;">
                    <div class="label_medium">Home Address</div>
                    <div class="body_small">1871 Sugar Creek Way<br />West Jordan, UT 84088</div>
                </div>
            </div>
        </div>

        <!-- **********iterate************ -->
        <app-contact-listing-month></app-contact-listing-month>

        <!-- ********************** -->

    </div>
</div>