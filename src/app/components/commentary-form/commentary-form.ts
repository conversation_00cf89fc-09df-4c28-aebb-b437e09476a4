import { Component } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { RouterLink } from '@angular/router';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-commentary-form',
  imports: [MatIconModule, RouterLink, MatDividerModule, MatButtonModule, MatInputModule],
  templateUrl: './commentary-form.html',
  styleUrl: './commentary-form.scss'
})
export class CommentaryForm {

}
