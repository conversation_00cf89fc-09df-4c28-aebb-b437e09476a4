<div class="surface">
    <span class="detail-header">
        <mat-icon class="header-icon" svgIcon="arrow_back" aria-hidden="false" aria-label="Back Icon"
            style="cursor: pointer;" routerLink="/contact-form"></mat-icon>
        <span class="title_large">Add Commentary</span>
    </span>
    <div class="surface-container-low">
        <span class="on-surface-variant">Date</span>
        <span class="body_large">March 12, 2025 | 1332 hrs</span>
        <span class="on-surface-variant">Primary Interviewer</span>
        <span class="body_large"><PERSON>, <PERSON></span>
        <span class="on-surface-variant">Secondary Interviewer</span>
        <span class="body_large"><PERSON>, <PERSON></span>
        <span class="on-surface-variant">Contact Type</span>
        <span class="body_large">Field Visit</span>
        <span class="on-surface-variant">Location</span>
        <span class="body_large">Primary Residence</span>
    </div>
    <div class="type-of-contact-btn-container">
        <button class="type-of-contact-btn on-surface-variant">Made Contact</button>
        <button class="type-of-contact-btn on-surface-variant">Attempted Contact</button>
    </div>
    <div class="surface-container-low on-surface-variant">
        <span>Summary of Contact</span>
        <textarea class="commentary-textarea body_large" placeholder="Include details related to the R&R efforts of the visit."></textarea>
        <span>Tap the mic next to your keyboard to initiate voice to text</span>
    </div>

    <div class="on-surface-variant">
        <span>
            <mat-icon class="info-field-icon">info_outline</mat-icon> Field Visit Guidelines
        </span>
    </div>

    <div>
        <mat-divider style="color: lightgray;"></mat-divider>
        <section>
            <div class="button-row">
                <button matButton="outlined">Cancel</button>
                <button style="background-color: #030303; color: white;" matButton="filled">Save</button>
            </div>
        </section>
    </div>
</div>